import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';

import '../providers/settings_provider.dart';
import 'equipment_api_service.dart';

class LifeSignalService {
  Timer? _lifeSignalTimer;
  final EquipmentApiService _apiService = EquipmentApiService();

  // Start sending life signals every 30-40 seconds random interval
  void startLifeSignal({required SettingsProvider settingsProvider}) {
    // Cancel existing timer if any
    stopLifeSignal();

    // Generate random delay between 30-40 seconds
    final random = Random();
    final duration = Duration(seconds: 30 + random.nextInt(13));

    // Start new timer with random interval
    _lifeSignalTimer = Timer.periodic(
      duration,
      (timer) => _apiService.sendHeartbeat(settingsProvider: settingsProvider),
    );
  }

  // Stop sending life signals
  void stopLifeSignal() {
    if (_lifeSignalTimer?.isActive == true) {
      _lifeSignalTimer?.cancel();
    }
  }
}
