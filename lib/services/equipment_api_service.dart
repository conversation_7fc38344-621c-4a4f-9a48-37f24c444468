import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../providers/settings_provider.dart';
import 'device_info_service.dart';

class EquipmentApiService {
  final Dio _dio = Dio();
  final DeviceInfoService _deviceInfoService = DeviceInfoService();

  Future<bool> registerDevice({
    required String serverAddress,
    required String serverPort,
    required String deviceAlias,
    required String macAddress,
    required String groupName,
    required String aliasName,
    required String registrationCode,
  }) async {
    if (serverAddress.isEmpty) {
      debugPrint('Server address is empty, skipping device registration');
      return false;
    }

    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult.contains(ConnectivityResult.none)) {
        debugPrint('No internet connection, skipping device registration');
        return false;
      }

      String ipAddress = await _getDeviceIpAddress() ?? '';
      String baseUrl = _buildBaseUrl(serverAddress, serverPort);
      final url = '$baseUrl/v1/equipment/addEquipment';

      final deviceInfo = await _deviceInfoService.getAndroidDetailedInfo();

      final body = {
        'name': deviceAlias,
        'mac_address': macAddress,
        'group_name': groupName,
        'alias_name': aliasName,
        'ip': ipAddress,
        'registration_code': registrationCode,
        ...deviceInfo,
      };

      debugPrint('Registering device with server: $url');
      debugPrint('Request body: $body');

      final response = await _dio.post(
        url,
        data: body,
        options: _getDioOptions(),
      );

      if (response.statusCode == 200) {
        debugPrint('Device registration successful: ${response.data}');
        return true;
      } else {
        debugPrint(
          'Device registration failed: ${response.statusCode}, ${response.data}',
        );
        return false;
      }
    } catch (e) {
      debugPrint('Error registering device: $e');
      return false;
    }
  }

  Future<bool> updateDeviceInfo({
    required String serverAddress,
    required String serverPort,
    required String deviceAlias,
    required String macAddress,
    required String groupName,
    required String aliasName,
    required String registrationCode,
  }) async {
    if (serverAddress.isEmpty) {
      debugPrint('Server address is empty, skipping device update');
      return false;
    }

    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult.contains(ConnectivityResult.none)) {
        debugPrint('No internet connection, skipping device update');
        return false;
      }

      String ipAddress = await _getDeviceIpAddress() ?? '';
      String baseUrl = _buildBaseUrl(serverAddress, serverPort);
      final url = '$baseUrl/v1/equipment/updateEquipment';

      final body = {
        'name': deviceAlias,
        'mac_address': macAddress,
        'group_name': groupName,
        'alias_name': aliasName,
        'ip': ipAddress,
        'registration_code': registrationCode,
      };

      debugPrint('Updating device info with server: $url');
      debugPrint('Request body: $body');

      final response = await _dio.put(
        url,
        data: body,
        options: _getDioOptions(),
      );

      if (response.statusCode == 200) {
        debugPrint('Device update successful: ${response.data}');
        return true;
      } else {
        debugPrint(
          'Device update failed: ${response.statusCode}, ${response.data}',
        );
        return false;
      }
    } catch (e) {
      debugPrint('Error updating device info: $e');
      return false;
    }
  }

  Future<void> sendHeartbeat({
    required SettingsProvider settingsProvider,
  }) async {
    try {
      final settings = settingsProvider.settings;
      final serverAddress = settings.mqttServerAddress ?? '';
      final macAddress = settings.macAddress ?? '';
      final registrationCode = settings.registrationCode ?? '';

      if (serverAddress.isEmpty || macAddress.isEmpty) {
        debugPrint('Missing required parameters for heartbeat');
        return;
      }

      String baseUrl = _buildBaseUrl(serverAddress, settings.serverPort ?? '');
      final url = '$baseUrl/v1/equipment/lifeEquipment';
      final body = {
        'mac_address': macAddress,
        'registration_code': registrationCode,
      };

      debugPrint('Sending heartbeat to: $url');
      debugPrint('Request body: $body');

      final response = await _dio.put(
        url,
        data: body,
        options: _getDioOptions(),
      );

      if (response.statusCode == 200) {
        debugPrint('Heartbeat sent successfully: ${response.data}');
        _handleHeartbeatResponse(response.data, settingsProvider);
      } else {
        debugPrint('Failed to send heartbeat, status: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      debugPrint('Error sending heartbeat: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  void _handleHeartbeatResponse(
    dynamic responseData,
    SettingsProvider settingsProvider,
  ) {
    try {
      if (responseData is Map<String, dynamic> &&
          responseData['code'] == 0 &&
          responseData['data'] != null) {
        final data = responseData['data'];
        final remoteGroupName = data['group_name']?.toString();
        final remoteAliasName = data['alias_name']?.toString();

        if (remoteGroupName != null &&
            remoteGroupName != settingsProvider.settings.groupName) {
          settingsProvider.updateGroupName(remoteGroupName);
        }

        if (remoteAliasName != null &&
            remoteAliasName != settingsProvider.settings.deviceAlias) {
          settingsProvider.updateDeviceAlias(remoteAliasName);
        }
      }
    } catch (e) {
      debugPrint('Error parsing heartbeat response: $e');
    }
  }

  String _buildBaseUrl(String serverAddress, String port) {
    if (!serverAddress.startsWith('http://') &&
        !serverAddress.startsWith('https://')) {
      return 'http://$serverAddress:$port';
    }
    return serverAddress;
  }

  Options _getDioOptions() {
    return Options(
      headers: {'Content-Type': 'application/json'},
      receiveTimeout: const Duration(seconds: 10),
      sendTimeout: const Duration(seconds: 10),
    );
  }

  Future<String?> _getDeviceIpAddress() async {
    try {
      final interfaces = await NetworkInterface.list(
        includeLoopback: false,
        type: InternetAddressType.IPv4,
      );
      for (var interface in interfaces) {
        for (var addr in interface.addresses) {
          if (addr.type == InternetAddressType.IPv4) {
            return addr.address;
          }
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error getting device IP address: $e');
      return null;
    }
  }
}
