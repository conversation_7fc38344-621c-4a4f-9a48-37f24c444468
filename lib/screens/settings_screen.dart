import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:udp/udp.dart';
import '../providers/mqtt_provider.dart';
import '../providers/settings_provider.dart';
import '../providers/localization_provider.dart';
import '../l10n/app_localizations_extension.dart';
import '../services/orientation_service.dart';
import '../utils/ui_utils.dart';
import '../widgets/custom_text_form_field.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _mqttServerController = TextEditingController();
  final _apiServerController = TextEditingController();
  final _mqttPortController = TextEditingController();
  final _groupNameController = TextEditingController();
  final _deviceAliasController = TextEditingController();

  String _selectedLanguage = 'zh';
  String _selectedOrientation = 'landscape';
  RawDatagramSocket? _socket;

  @override
  void initState() {
    super.initState();
    UiUtils.setFullscreenMode();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final settingsProvider = Provider.of<SettingsProvider>(
        context,
        listen: false,
      );
      final settings = settingsProvider.settings;
      _mqttServerController.text = settings.mqttServerAddress ?? '';
      _apiServerController.text = settings.serverPort ?? '8090';
      _mqttPortController.text = settings.mqttPort ?? '1883';
      _groupNameController.text = settings.groupName ?? '';
      _deviceAliasController.text = settings.deviceAlias ?? '';

      setState(() {
        _selectedLanguage = settings.languageCode ?? 'zh';
        _selectedOrientation = settings.screenOrientation ?? 'landscape';
      });

      if (_mqttServerController.text.isEmpty) {
        _listenForBroadcast();
      }
    });
  }

  @override
  void dispose() {
    _mqttServerController.dispose();
    _apiServerController.dispose();
    _mqttPortController.dispose();
    _groupNameController.dispose();
    _deviceAliasController.dispose();
    _socket?.close();
    super.dispose();
  }

  Future<void> _listenForBroadcast() async {
    try {
      _socket = await RawDatagramSocket.bind(InternetAddress.anyIPv4, 9999);
      _socket?.broadcastEnabled = true;
      _socket?.listen((RawSocketEvent event) {
        if (event == RawSocketEvent.read) {
          final datagram = _socket?.receive();
          if (datagram != null) {
            final message = String.fromCharCodes(datagram.data);
            if (message.startsWith('SERVER_ADDRESS=')) {
              final address = message.split('=')[1];
              if (mounted) {
                setState(() {
                  _mqttServerController.text = address;
                });
                _submit(isAutoDiscovered: true);
              }
              _socket?.close();
              _socket = null;
            }
          }
        }
      });
    } catch (e) {
      // Handle exceptions
    }
  }

  Future<bool> _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return false;
    }

    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final localizationProvider = Provider.of<LocalizationProvider>(
      context,
      listen: false,
    );

    await settingsProvider.updateMqttServerAddress(_mqttServerController.text);
    await settingsProvider.updateMqttPort(_mqttPortController.text);
    await settingsProvider.updateGroupName(_groupNameController.text);
    await settingsProvider.updateDeviceAlias(_deviceAliasController.text);

    if (_selectedLanguage != settingsProvider.settings.languageCode) {
      await settingsProvider.updateLanguageCode(_selectedLanguage);
      await localizationProvider.changeLocale(Locale(_selectedLanguage));
    }

    if (_selectedOrientation != settingsProvider.settings.screenOrientation) {
      await settingsProvider.updateScreenOrientation(_selectedOrientation);
      await OrientationService.applyOrientation(_selectedOrientation);
    }
    return true;
  }

  Future<void> _submit({bool isAutoDiscovered = false}) async {
    final saved = await _saveSettings();
    if (saved && mounted) {
      if (!isAutoDiscovered) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(context.l10n.settingsSaved)));
      }
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(context.l10n.settings)),
      body: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(20, 4, 20, 4),
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildLanguageSelection(),
                    const SizedBox(height: 1),
                    _buildOrientationSelection(),
                    const SizedBox(height: 1),
                    CustomTextFormField(
                      controller: _mqttServerController,
                      labelText: context.l10n.mqttServerAddress,
                      hintText: context.l10n.mqttServerAddressHint,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return context.l10n.pleaseEnterMqttServerAddress;
                        }
                        return null;
                      },
                    ),
                    CustomTextFormField(
                      controller: _apiServerController,
                      labelText: context.l10n.apiServerPort,
                      hintText: context.l10n.apiServerPortHint,
                      keyboardType: TextInputType.number,
                      validator: _validatePort,
                    ),
                    CustomTextFormField(
                      controller: _mqttPortController,
                      labelText: context.l10n.mqttPort,
                      hintText: context.l10n.mqttPortHint,
                      keyboardType: TextInputType.number,
                      validator: _validatePort,
                    ),
                    CustomTextFormField(
                      controller: _groupNameController,
                      labelText: context.l10n.groupName,
                      hintText: context.l10n.groupNameHint,
                    ),
                    CustomTextFormField(
                      controller: _deviceAliasController,
                      labelText: context.l10n.deviceAlias,
                      hintText: context.l10n.deviceAliasHint,
                    ),
                    TextFormField(
                      initialValue:
                          settingsProvider.settings.macAddress ??
                          context.l10n.loading,
                      decoration: InputDecoration(
                        labelText: context.l10n.macAddress,
                      ),
                      readOnly: true,
                    ),
                    const SizedBox(height: 18),
                    Center(
                      child: ElevatedButton(
                        onPressed: settingsProvider.isLoading ? null : _submit,
                        child: settingsProvider.isLoading
                            ? const CircularProgressIndicator()
                            : Text(context.l10n.saveSettings),
                      ),
                    ),
                    if (settingsProvider.error.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Text(
                          settingsProvider.error,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLanguageSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.l10n.language,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        Row(
          children: [
            Expanded(
              child: RadioListTile<String>(
                title: Text(context.l10n.english),
                value: 'en',
                groupValue: _selectedLanguage,
                onChanged: (value) =>
                    setState(() => _selectedLanguage = value!),
              ),
            ),
            Expanded(
              child: RadioListTile<String>(
                title: Text(context.l10n.chinese),
                value: 'zh',
                groupValue: _selectedLanguage,
                onChanged: (value) =>
                    setState(() => _selectedLanguage = value!),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOrientationSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.l10n.screenOrientation,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        Row(
          children: [
            Expanded(
              child: RadioListTile<String>(
                title: Text(context.l10n.landscapeDisplay),
                value: 'landscape',
                groupValue: _selectedOrientation,
                onChanged: (value) =>
                    setState(() => _selectedOrientation = value!),
              ),
            ),
            Expanded(
              child: RadioListTile<String>(
                title: Text(context.l10n.portraitDisplay),
                value: 'portrait',
                groupValue: _selectedOrientation,
                onChanged: (value) =>
                    setState(() => _selectedOrientation = value!),
              ),
            ),
          ],
        ),
      ],
    );
  }

  String? _validatePort(String? value) {
    if (value == null || value.isEmpty) {
      return context.l10n.pleaseEnterMqttPort;
    }
    try {
      final port = int.parse(value);
      if (port <= 0 || port > 65535) {
        return context.l10n.portNumberMustBeBetween;
      }
    } catch (e) {
      return context.l10n.pleaseEnterValidPort;
    }
    return null;
  }
}
