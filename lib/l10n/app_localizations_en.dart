// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'ESOP Client';

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get english => 'English';

  @override
  String get chinese => 'Chinese';

  @override
  String get settingsSaved => 'Settings saved';

  @override
  String get mqttServerAddress => 'Server Address';

  @override
  String get mqttServerAddressHint => 'e.g., example.com or ip addr';

  @override
  String get pleaseEnterMqttServerAddress => 'Please enter server address';

  @override
  String get apiServerPort => 'Api Port';

  @override
  String get apiServerPortHint => 'e.g., 8090';

  @override
  String get mqttPort => 'MQTT Port';

  @override
  String get mqttPortHint => 'e.g., 1883';

  @override
  String get pleaseEnterMqttPort => 'Please enter MQTT port';

  @override
  String get portNumberMustBeBetween => 'Port number must be between 1-65535';

  @override
  String get pleaseEnterValidPort => 'Please enter a valid port number';

  @override
  String get mqttTopic => 'MQTT Topic';

  @override
  String get mqttTopicHint => 'e.g., esopChannel';

  @override
  String get pleaseEnterMqttTopic => 'Please enter MQTT topic';

  @override
  String get groupName => 'Group Name';

  @override
  String get groupNameHint => 'e.g., Production Line 1';

  @override
  String get pleaseEnterGroupName => 'Please enter group name';

  @override
  String get deviceAlias => 'Device Alias';

  @override
  String get deviceAliasHint => 'e.g., My Device';

  @override
  String get pleaseEnterDeviceAlias => 'Please enter device alias';

  @override
  String get registrationCode => 'Registration Code';

  @override
  String get registrationCodeHint => 'e.g., ABC123';

  @override
  String get pleaseEnterRegistrationCode => 'Please enter registration code';

  @override
  String get deviceId => 'Device ID';

  @override
  String get macAddress => 'MAC Address';

  @override
  String get loading => 'Loading...';

  @override
  String get saveSettings => 'Save Settings';

  @override
  String get connected => 'Connected';

  @override
  String get disconnected => 'Disconnected';

  @override
  String mqttStatus(String status) {
    return 'MQTT Status: $status';
  }

  @override
  String get connect => 'Connect';

  @override
  String get doubleTapWithTwoFingers => 'Double tap with to settings';

  @override
  String fileOperation(String status) {
    return 'File Operation: $status';
  }

  @override
  String get retry => 'Retry';

  @override
  String get usingExistingFile => 'Using existing file';

  @override
  String get checking => 'Checking';

  @override
  String get openingDocument => 'Opening document...';

  @override
  String errorLoadingContent(String message) {
    return 'Error loading content: $message';
  }

  @override
  String httpError(int statusCode, String description) {
    return 'HTTP Error: $statusCode $description';
  }

  @override
  String get error => 'Error';

  @override
  String get back => 'Back';

  @override
  String get screenOrientation => 'Screen Orientation';

  @override
  String get landscapeDisplay => 'Landscape Display';

  @override
  String get portraitDisplay => 'Portrait Display';
}
